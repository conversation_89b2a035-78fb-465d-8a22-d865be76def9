import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ChatAi } from "./entities/chatAi.entity";
import { ChatAiDocument } from "./entities/document.entity";
import { ChatAiMessage } from "./entities/message.entity";
import { ChatAiCreditUsage } from "./entities/credit-usage.entity";
import { ChatAiApiTransaction } from "./entities/transaction.entity";
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
} from "./dto/chatAi.dto";
import { Application } from "../application/entities/application.entity";

@Injectable()
export class ChatAiService {
  private readonly logger = new Logger(ChatAiService.name);

  constructor(
    @InjectRepository(ChatAi)
    private readonly chatAiRepository: Repository<ChatAi>,

    @InjectRepository(ChatAiDocument)
    private readonly documentRepository: Repository<ChatAiDocument>,

    @InjectRepository(ChatAiMessage)
    private readonly messageRepository: Repository<ChatAiMessage>,

    @InjectRepository(ChatAiCreditUsage)
    private readonly creditUsageRepository: Repository<ChatAiCreditUsage>,

    @InjectRepository(ChatAiApiTransaction)
    private readonly apiTransactionRepository: Repository<ChatAiApiTransaction>,

    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>
  ) {}

  // ==================== ChatAI Project Service Methods ====================

  async createChatAiProject(
    createChatAiDto: CreateChatAiDto,
    appId: string
  ): Promise<ChatAi> {
    try {
      // Find the application
      const application = await this.applicationRepository.findOne({
        where: { id: appId },
      });

      if (!application) {
        throw new NotFoundException("Application not found");
      }

      // Check if ChatAI project already exists for this application
      const existingProject = await this.chatAiRepository.findOne({
        where: { app: { id: appId } },
      });

      if (existingProject) {
        throw new BadRequestException(
          "ChatAI project already exists for this application. Only one project allowed per application."
        );
      }

      // Create ChatAI project
      const chatAi = this.chatAiRepository.create({
        name: createChatAiDto.chatAiName,
        description: createChatAiDto.description || null,
        userId: createChatAiDto.userId,
        llamaCloudApiKey: createChatAiDto.llamaCloudApiKey,
        openRouterApiKey: createChatAiDto.openRouterApiKey,
        credits: createChatAiDto.credits || 30,
        subscriptionStatus: createChatAiDto.subscriptionStatus || "free",
        notificationsEnabled: createChatAiDto.notificationsEnabled || false,
        notificationEmail: createChatAiDto.notificationEmail,
        app: application,
      });

      const savedChatAi = await this.chatAiRepository.save(chatAi);

      // Deduct credit for project creation
      await this.checkAndDeductCredits(
        savedChatAi.id,
        createChatAiDto.userId,
        "project_create",
        1
      );

      this.logger.log(`ChatAI project created successfully: ${savedChatAi.id}`);

      return savedChatAi;
    } catch (error) {
      this.logger.error(`Failed to create ChatAI project: ${error.message}`);
      throw error;
    }
  }

  async findChatAiProjectByAppId(appId: string): Promise<ChatAi> {
    const chatAi = await this.chatAiRepository.findOne({
      where: { app: { id: appId } },
      relations: ["app", "documents", "messages"],
    });

    if (!chatAi) {
      throw new NotFoundException(
        "ChatAI project not found for this application"
      );
    }

    return chatAi;
  }

  async findChatAiProjectByUser(userId: string): Promise<ChatAi | null> {
    return this.chatAiRepository.findOne({
      where: {
        userId,
        status: "active",
      },
      relations: ["documents", "app"],
    });
  }

  async updateChatAiProject(
    id: string,
    updateChatAiDto: UpdateChatAiDto
  ): Promise<ChatAi> {
    const chatAi = await this.chatAiRepository.findOne({ where: { id } });

    if (!chatAi) {
      throw new NotFoundException("ChatAI project not found");
    }

    Object.assign(chatAi, updateChatAiDto);
    const updatedChatAi = await this.chatAiRepository.save(chatAi);

    this.logger.log(`ChatAI project updated successfully: ${id}`);
    return updatedChatAi;
  }

  // ==================== Document Management ====================

  async createDocument(
    createDocumentDto: CreateDocumentDto,
    projectId: string
  ): Promise<ChatAiDocument> {
    try {
      // Check if project exists and belongs to user
      const project = await this.chatAiRepository.findOne({
        where: { id: projectId, userId: createDocumentDto.userId },
      });

      if (!project) {
        throw new NotFoundException("Project not found or access denied");
      }

      // Check credit limits
      await this.checkAndDeductCredits(
        projectId,
        createDocumentDto.userId,
        "document_upload",
        1
      );

      // Create document
      const document = this.documentRepository.create({
        ...createDocumentDto,
        projectId,
        project,
      });

      const savedDocument = await this.documentRepository.save(document);
      this.logger.log(`Document created successfully: ${savedDocument.id}`);

      return savedDocument;
    } catch (error) {
      this.logger.error(`Failed to create document: ${error.message}`);
      throw error;
    }
  }

  async findDocumentsByProject(
    projectId: string,
    userId: string
  ): Promise<ChatAiDocument[]> {
    // Verify project ownership
    const project = await this.chatAiRepository.findOne({
      where: { id: projectId, userId },
    });

    if (!project) {
      throw new NotFoundException("Project not found or access denied");
    }

    return this.documentRepository.find({
      where: { projectId },
      order: { createdAt: "DESC" },
    });
  }

  async updateDocument(
    id: number,
    updateDocumentDto: UpdateDocumentDto,
    userId: string
  ): Promise<ChatAiDocument> {
    const document = await this.documentRepository.findOne({
      where: { id, userId },
    });

    if (!document) {
      throw new NotFoundException("Document not found");
    }

    Object.assign(document, updateDocumentDto);
    const updatedDocument = await this.documentRepository.save(document);

    this.logger.log(`Document updated successfully: ${id}`);
    return updatedDocument;
  }

  // ==================== Chat Management ====================

  async createChatMessage(
    createChatMessageDto: CreateChatMessageDto,
    projectId: string
  ): Promise<ChatAiMessage> {
    try {
      // Verify project exists
      const project = await this.chatAiRepository.findOne({
        where: { id: projectId },
      });

      if (!project) {
        throw new NotFoundException("Project not found");
      }

      // Create message
      const message = this.messageRepository.create({
        ...createChatMessageDto,
        projectId,
        project,
      });

      const savedMessage = await this.messageRepository.save(message);
      this.logger.log(`Chat message created successfully: ${savedMessage.id}`);

      return savedMessage;
    } catch (error) {
      this.logger.error(`Failed to create chat message: ${error.message}`);
      throw error;
    }
  }

  async findChatHistory(
    projectId: string,
    userId: string,
    limit: number = 50
  ): Promise<ChatAiMessage[]> {
    // Verify project ownership
    const project = await this.chatAiRepository.findOne({
      where: { id: projectId, userId },
    });

    if (!project) {
      throw new NotFoundException("Project not found or access denied");
    }

    return this.messageRepository.find({
      where: { projectId },
      order: { timestamp: "DESC" },
      take: limit,
    });
  }

  // ==================== Credit Management ====================

  async checkAndDeductCredits(
    projectId: string,
    userId: string,
    actionType: string,
    creditsRequired: number
  ): Promise<void> {
    const project = await this.chatAiRepository.findOne({
      where: { id: projectId },
    });

    if (!project) {
      throw new NotFoundException("Project not found");
    }

    // Check if user has enough credits
    if (project.credits < creditsRequired) {
      throw new BadRequestException("Insufficient credits");
    }

    // Deduct credits
    project.credits -= creditsRequired;
    project.totalCreditsUsed += creditsRequired;
    await this.chatAiRepository.save(project);

    // Log credit usage
    const creditUsage = this.creditUsageRepository.create({
      projectId,
      userId,
      actionType,
      creditsUsed: creditsRequired,
    });

    await this.creditUsageRepository.save(creditUsage);
    this.logger.log(
      `Credits deducted: ${creditsRequired} for action: ${actionType}`
    );
  }

  async getCreditUsage(
    projectId: string,
    userId: string
  ): Promise<ChatAiCreditUsage[]> {
    return this.creditUsageRepository.find({
      where: { projectId, userId },
      order: { timestamp: "DESC" },
      take: 100,
    });
  }

  // ==================== API Transaction Logging ====================

  async logApiTransaction(
    projectId: string,
    apiProvider: string,
    endpoint: string,
    requestType: string,
    requestData: any,
    responseData: any,
    status: string,
    tokensUsed?: number,
    cost?: number,
    errorMessage?: string
  ): Promise<ChatAiApiTransaction> {
    const transaction = this.apiTransactionRepository.create({
      projectId,
      apiProvider,
      endpoint,
      requestType,
      requestData,
      responseData,
      status,
      tokensUsed,
      cost,
      errorMessage,
    });

    return this.apiTransactionRepository.save(transaction);
  }
}
